#!/usr/bin/env python3
"""
Advanced Faucet Tool - Thử bypass Cloudflare Turnstile và tìm API thực tế
"""

import asyncio
import aiohttp
import json
import time
import random
from urllib.parse import urljoin

class AdvancedFaucetTool:
    def __init__(self, address):
        self.address = address
        self.session = None
        
    async def init_session(self):
        """Khởi tạo session với headers giống browser thật."""
        connector = aiohttp.TCPConnector()
        self.session = aiohttp.ClientSession(
            connector=connector,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0'
            }
        )
    
    async def get_page_with_session(self, url):
        """Lấy trang với session persistence."""
        try:
            print(f"🌐 Truy cập: {url}")
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    print(f"✅ Tải trang thành công ({len(html)} chars)")
                    
                    # Tìm CSRF token hoặc session token
                    import re
                    csrf_matches = re.findall(r'csrf["\']?\s*:\s*["\']([^"\']+)["\']', html, re.IGNORECASE)
                    if csrf_matches:
                        print(f"🔑 Tìm thấy CSRF token: {csrf_matches[0][:20]}...")
                        return html, csrf_matches[0]
                    
                    # Tìm các token khác
                    token_patterns = [
                        r'token["\']?\s*:\s*["\']([^"\']+)["\']',
                        r'_token["\']?\s*:\s*["\']([^"\']+)["\']',
                        r'authenticity_token["\']?\s*:\s*["\']([^"\']+)["\']'
                    ]
                    
                    for pattern in token_patterns:
                        matches = re.findall(pattern, html, re.IGNORECASE)
                        if matches:
                            print(f"🔑 Tìm thấy token: {matches[0][:20]}...")
                            return html, matches[0]
                    
                    return html, None
                else:
                    print(f"❌ Lỗi {response.status}")
                    return None, None
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            return None, None
    
    async def try_faucet_with_session(self):
        """Thử faucet với session và token."""
        print(f"💰 Thử claim ETH cho địa chỉ: {self.address}")
        
        # 1. Lấy trang faucet trước
        html, token = await self.get_page_with_session("https://faucet.risechain.com/")
        if not html:
            return False
        
        # Đợi một chút để giống user thật
        await asyncio.sleep(random.uniform(2, 5))
        
        # 2. Thử các endpoint với session và token
        endpoints_to_try = [
            "/api/faucet",
            "/api/drip", 
            "/api/claim",
            "/api/request",
            "/faucet",
            "/drip"
        ]
        
        for endpoint in endpoints_to_try:
            success = await self.try_endpoint_with_token(endpoint, token)
            if success:
                return True
            
            # Đợi giữa các request
            await asyncio.sleep(random.uniform(1, 3))
        
        return False
    
    async def try_endpoint_with_token(self, endpoint, token):
        """Thử một endpoint cụ thể với token."""
        url = f"https://faucet.risechain.com{endpoint}"
        print(f"🔄 Thử: {endpoint}")
        
        # Chuẩn bị headers cho request
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://faucet.risechain.com',
            'Referer': 'https://faucet.risechain.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        
        # Thêm token nếu có
        if token:
            headers['X-CSRF-Token'] = token
            headers['X-Requested-With'] = 'XMLHttpRequest'
        
        # Chuẩn bị payload
        payloads_to_try = [
            {"address": self.address},
            {"walletAddress": self.address},
            {"recipient": self.address},
            {"to": self.address},
            {"address": self.address, "token": "ETH"},
            {"address": self.address, "csrf_token": token} if token else {"address": self.address}
        ]
        
        for payload in payloads_to_try:
            try:
                async with self.session.post(url, json=payload, headers=headers) as response:
                    text = await response.text()
                    
                    if response.status == 200:
                        print(f"✅ Thành công! {endpoint} - Response: {text}")
                        return True
                    elif response.status == 429:
                        print(f"⏰ Rate limited - {endpoint}")
                        return False
                    elif response.status not in [404, 405]:
                        print(f"🔍 {endpoint} - {response.status}: {text[:100]}")
                        
            except Exception as e:
                continue
        
        return False
    
    async def close(self):
        """Đóng session."""
        if self.session:
            await self.session.close()

async def main():
    # Sử dụng địa chỉ test
    address = "******************************************"
    
    print("🚀 Advanced Faucet Tool - RiseChain ETH Faucet")
    print("="*60)
    
    tool = AdvancedFaucetTool(address)
    
    try:
        await tool.init_session()
        success = await tool.try_faucet_with_session()
        
        if success:
            print("\n🎉 Thành công! ETH đã được gửi đến ví của bạn!")
        else:
            print("\n❌ Không thể tự động claim ETH")
            print("💡 Giải pháp thay thế:")
            print("   1. Truy cập: https://faucet.risechain.com/")
            print(f"   2. Nhập địa chỉ: {address}")
            print("   3. Hoàn thành Captcha và click 'Drip'")
            print("   4. Hoặc thử Discord: https://discord.com/invite/risechain")
            
    finally:
        await tool.close()

if __name__ == "__main__":
    asyncio.run(main())
