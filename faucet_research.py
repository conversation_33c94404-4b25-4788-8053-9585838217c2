#!/usr/bin/env python3
"""
Faucet Research Tool - Tìm API endpoint thực tế của RiseChain faucet
"""

import asyncio
import aiohttp
import json
import re
from urllib.parse import urljoin

class FaucetResearcher:
    def __init__(self):
        self.base_urls = [
            "https://faucet.risechain.com",
            "https://portal.risechain.com"
        ]
        self.test_address = "0x1Be31A94361a391bBaFB2a4CCd704F57dc04d4bb"
        
    async def research_endpoints(self):
        """Nghiên cứu và tìm API endpoints thực tế."""
        print("🔍 Bắt đầu nghiên cứu faucet endpoints...")
        
        async with aiohttp.ClientSession() as session:
            for base_url in self.base_urls:
                print(f"\n📡 Nghiên cứu: {base_url}")
                
                # 1. <PERSON><PERSON><PERSON> trang chính để phân tích
                await self.analyze_main_page(session, base_url)
                
                # 2. Thử các endpoint phổ biến
                await self.test_common_endpoints(session, base_url)
                
                # 3. T<PERSON>m kiếm trong JavaScript files
                await self.search_js_files(session, base_url)
    
    async def analyze_main_page(self, session, base_url):
        """Phân tích trang chính để tìm clues."""
        try:
            async with session.get(base_url) as response:
                if response.status == 200:
                    html = await response.text()
                    
                    # Tìm các script tags
                    script_matches = re.findall(r'<script[^>]*src="([^"]*)"', html)
                    print(f"  📜 Tìm thấy {len(script_matches)} script files")
                    
                    # Tìm các API calls trong HTML
                    api_matches = re.findall(r'["\']([^"\']*api[^"\']*)["\']', html)
                    if api_matches:
                        print(f"  🔗 Potential API endpoints: {set(api_matches)}")
                    
                    # Tìm form actions
                    form_matches = re.findall(r'<form[^>]*action="([^"]*)"', html)
                    if form_matches:
                        print(f"  📝 Form actions: {form_matches}")
                        
        except Exception as e:
            print(f"  ❌ Lỗi phân tích trang chính: {e}")
    
    async def test_common_endpoints(self, session, base_url):
        """Test các endpoint phổ biến."""
        endpoints = [
            "/api/faucet",
            "/api/drip", 
            "/api/claim",
            "/api/request",
            "/api/v1/faucet",
            "/api/v1/drip",
            "/faucet/drip",
            "/drip",
            "/_next/api/faucet",
            "/_next/api/drip"
        ]
        
        print(f"  🧪 Testing {len(endpoints)} common endpoints...")
        
        for endpoint in endpoints:
            url = urljoin(base_url, endpoint)
            
            # Test POST
            try:
                async with session.post(
                    url,
                    json={"address": self.test_address},
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status not in [404, 405]:
                        text = await response.text()
                        print(f"    ✅ POST {endpoint}: {response.status} - {text[:100]}")
            except:
                pass
            
            # Test GET
            try:
                async with session.get(f"{url}?address={self.test_address}") as response:
                    if response.status not in [404, 405]:
                        text = await response.text()
                        print(f"    ✅ GET {endpoint}: {response.status} - {text[:100]}")
            except:
                pass
    
    async def search_js_files(self, session, base_url):
        """Tìm kiếm trong JavaScript files."""
        try:
            # Lấy trang chính để tìm JS files
            async with session.get(base_url) as response:
                html = await response.text()
                
            # Extract JS file URLs
            js_files = re.findall(r'<script[^>]*src="([^"]*\.js[^"]*)"', html)
            
            print(f"  🔍 Phân tích {len(js_files)} JavaScript files...")
            
            for js_file in js_files[:5]:  # Chỉ check 5 files đầu
                js_url = urljoin(base_url, js_file)
                try:
                    async with session.get(js_url) as response:
                        if response.status == 200:
                            js_content = await response.text()
                            
                            # Tìm API endpoints trong JS
                            api_patterns = [
                                r'["\']([^"\']*api[^"\']*faucet[^"\']*)["\']',
                                r'["\']([^"\']*faucet[^"\']*api[^"\']*)["\']',
                                r'["\']([^"\']*drip[^"\']*)["\']',
                                r'fetch\(["\']([^"\']*)["\']',
                                r'axios\.[a-z]+\(["\']([^"\']*)["\']'
                            ]
                            
                            for pattern in api_patterns:
                                matches = re.findall(pattern, js_content)
                                if matches:
                                    print(f"    🎯 Found in {js_file}: {set(matches)}")
                                    
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"  ❌ Lỗi tìm kiếm JS files: {e}")

async def main():
    researcher = FaucetResearcher()
    await researcher.research_endpoints()
    
    print("\n" + "="*60)
    print("📋 KẾT LUẬN:")
    print("1. Nếu không tìm thấy API endpoint, sử dụng browser manual")
    print("2. Truy cập: https://faucet.risechain.com/")
    print("3. Nhập địa chỉ ví và click 'Drip'")
    print("4. Hoặc thử Discord faucet: https://discord.com/invite/risechain")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
