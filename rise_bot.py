# -*- coding: utf-8 -*-

import asyncio
import json
import os
import random
import pytz
import time
import aiohttp
import re
from datetime import datetime
from colorama import Fore, Style, init
from web3 import AsyncWeb3
from eth_account import Account

# Khởi tạo colorama
init(autoreset=True)

# --- PHẦN CẤU HÌNH ---
# D<PERSON> liệu đã được cập nhật từ Block Explorer
# ==============================================================================

# 1. RPC Endpoint của Risechain Testnet
RISECHAIN_TESTNET_RPC = "https://testnet.riselabs.xyz"

# 1.1. ETH Faucet URLs - Backup faucet endpoints
ETH_FAUCET_URLS = [
    "https://faucet.risechain.com/api/faucet",
    "https://faucet.risechain.com/api/drip",
    "https://faucet.risechain.com/api/request",
    "https://faucet.risechain.com/api/claim",
    "https://faucet.risechain.com/faucet",
    "https://faucet.risechain.com/request"
]

# 1.2. YesCaptcha Configuration
YESCAPTCHA_API_KEY = ""  # Sẽ được nhập từ user
YESCAPTCHA_API_URL = "https://api.yescaptcha.com"

# 2. Thông tin FAUCET
FAUCET_CONTRACT_ADDRESS = "******************************************"
FAUCET_ABI = [
    {"inputs":[],"stateMutability":"nonpayable","type":"constructor"},
    {"inputs":[{"internalType":"address","name":"token","type":"address"}],"name":"drip","outputs":[],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}
]

# 3. Thông tin SWAP ROUTER
SWAP_ROUTER_ADDRESS = "******************************************"
SWAP_ROUTER_ABI = [
    {"inputs":[{"internalType":"address","name":"_factory","type":"address"},{"internalType":"address","name":"_weth","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},
    {"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"uint256","name":"amountOutMin","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"swapExactTokensForTokens","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"nonpayable","type":"function"}
]

# 4. Địa chỉ các token phổ biến
TOKEN_ADDRESSES = {
    "WRISE": "******************************************",
    "USDC": "******************************************",
    "BUSD": "******************************************"
}

# ABI tiêu chuẩn của token ERC20
ERC20_ABI = [
    {"constant":True,"inputs":[],"name":"name","outputs":[{"name":"","type":"string"}],"payable":False,"stateMutability":"view","type":"function"},
    {"constant":False,"inputs":[{"name":"_spender","type":"address"},{"name":"_value","type":"uint256"}],"name":"approve","outputs":[{"name":"","type":"bool"}],"payable":False,"stateMutability":"nonpayable","type":"function"},
    {"constant":True,"inputs":[],"name":"decimals","outputs":[{"name":"","type":"uint8"}],"payable":False,"stateMutability":"view","type":"function"},
    {"constant":True,"inputs":[{"name":"_owner","type":"address"}],"name":"balanceOf","outputs":[{"name":"balance","type":"uint256"}],"payable":False,"stateMutability":"view","type":"function"},
    {"constant":True,"inputs":[],"name":"symbol","outputs":[{"name":"","type":"string"}],"payable":False,"stateMutability":"view","type":"function"}
]

# Múi giờ Việt Nam
VN_TZ = pytz.timezone('Asia/Ho_Chi_Minh')

# ==============================================================================
# YesCaptcha Solver Class
# ==============================================================================

class YesCaptchaSolver:
    """Class để giải Cloudflare Turnstile bằng YesCaptcha API."""

    def __init__(self, api_key):
        self.api_key = api_key
        self.api_url = YESCAPTCHA_API_URL

    async def solve_turnstile(self, session, site_key, page_url):
        """Giải Cloudflare Turnstile captcha."""
        print(f"    🔐 Đang giải Cloudflare Turnstile...")

        try:
            # 1. Gửi task giải captcha
            task_id = await self.submit_task(session, site_key, page_url)
            if not task_id:
                return None

            # 2. Chờ và lấy kết quả
            result = await self.get_task_result(session, task_id)
            return result

        except Exception as e:
            print(f"    ❌ Lỗi giải captcha: {str(e)}")
            return None

    async def submit_task(self, session, site_key, page_url):
        """Gửi task giải captcha."""
        payload = {
            "clientKey": self.api_key,
            "task": {
                "type": "TurnstileTaskProxyless",
                "websiteURL": page_url,
                "websiteKey": site_key
            }
        }

        try:
            async with session.post(
                f"{self.api_url}/createTask",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("errorId") == 0:
                        task_id = data.get("taskId")
                        print(f"    📝 Task ID: {task_id}")
                        return task_id
                    else:
                        print(f"    ❌ Lỗi tạo task: {data.get('errorDescription')}")
                        return None
                else:
                    print(f"    ❌ HTTP Error: {response.status}")
                    return None
        except Exception as e:
            print(f"    ❌ Lỗi submit task: {str(e)}")
            return None

    async def get_task_result(self, session, task_id, max_attempts=30):
        """Lấy kết quả giải captcha."""
        payload = {
            "clientKey": self.api_key,
            "taskId": task_id
        }

        for attempt in range(max_attempts):
            try:
                await asyncio.sleep(3)  # Đợi 3 giây giữa các lần check

                async with session.post(
                    f"{self.api_url}/getTaskResult",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get("errorId") == 0:
                            if data.get("status") == "ready":
                                token = data.get("solution", {}).get("token")
                                print(f"    ✅ Captcha đã giải xong!")
                                return token
                            elif data.get("status") == "processing":
                                print(f"    ⏳ Đang xử lý... ({attempt + 1}/{max_attempts})")
                                continue
                            else:
                                print(f"    ❌ Status không xác định: {data.get('status')}")
                                return None
                        else:
                            print(f"    ❌ Lỗi: {data.get('errorDescription')}")
                            return None
                    else:
                        print(f"    ❌ HTTP Error: {response.status}")
                        return None

            except Exception as e:
                print(f"    ❌ Lỗi get result: {str(e)}")
                continue

        print(f"    ⏰ Timeout - không thể giải captcha trong {max_attempts * 3} giây")
        return None

# ==============================================================================
# RiseChain Bot Class
# ==============================================================================

class RiseChainBot:
    """Bot tự động hóa cho Risechain, hỗ trợ Faucet và Swap."""
    def __init__(self, private_key: str, proxy: str = None):
        self.proxy = proxy
        request_kwargs = {}
        if self.proxy and self.proxy.startswith(('http://', 'https://')):
            request_kwargs = {'proxy': self.proxy}
        
        self.w3 = AsyncWeb3(AsyncWeb3.AsyncHTTPProvider(RISECHAIN_TESTNET_RPC, request_kwargs=request_kwargs))
        self.account = Account.from_key(private_key)
        self.address = self.account.address
        self.session = None

    async def _send_transaction(self, tx_data):
        """Hàm chung để ký và gửi một giao dịch."""
        signed_tx = self.account.sign_transaction(tx_data)
        tx_hash = await self.w3.eth.send_raw_transaction(signed_tx.raw_transaction)
        explorer_link = f"https://explorer.testnet.riselabs.xyz/tx/{self.w3.to_hex(tx_hash)}"
        log(f"    ✅ Giao dịch đã được gửi! Tx Hash: {self.w3.to_hex(tx_hash)}")
        log(f"    🔗 Link Explorer: {explorer_link}")
        log("    ⏳ Đang chờ xác nhận...")
        receipt = await self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if receipt.status == 1:
            log(f"    🎉 Giao dịch thành công tại block: {receipt.blockNumber}")
        else:
            log(f"    ❌ Giao dịch thất bại tại block: {receipt.blockNumber}")
        return self.w3.to_hex(tx_hash)

    async def drip_faucet(self, token_symbol: str):
        """Yêu cầu token từ Faucet."""
        log(f"  💧 Đang yêu cầu token {token_symbol} từ Faucet...")
        try:
            token_address = self.w3.to_checksum_address(TOKEN_ADDRESSES[token_symbol.upper()])
            faucet_contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(FAUCET_CONTRACT_ADDRESS),
                abi=FAUCET_ABI
            )
            
            tx_data = await faucet_contract.functions.drip(token_address).build_transaction({
                'from': self.address,
                'nonce': await self.w3.eth.get_transaction_count(self.address),
                'gas': 200000,
                'gasPrice': await self.w3.eth.gas_price,
                'chainId': await self.w3.eth.chain_id
            })
            return await self._send_transaction(tx_data)
        except KeyError:
            log(f"    ❌ Lỗi: Token '{token_symbol}' không được định nghĩa trong TOKEN_ADDRESSES.")
            return None
        except Exception as e:
            log(f"    ❌ Lỗi khi drip Faucet: {str(e)}")
            return None

    async def faucet_eth(self, yescaptcha_api_key=None):
        """Yêu cầu ETH từ faucet API với YesCaptcha bypass."""
        log(f"  💰 Đang yêu cầu ETH từ faucet...")

        if not self.session:
            connector = aiohttp.TCPConnector()
            if self.proxy:
                self.session = aiohttp.ClientSession(connector=connector, proxy=self.proxy)
            else:
                self.session = aiohttp.ClientSession(connector=connector)

        # Nếu có YesCaptcha API key, thử phương pháp bypass
        if yescaptcha_api_key:
            success = await self.faucet_with_captcha_bypass(yescaptcha_api_key)
            if success:
                return True

        for faucet_url in ETH_FAUCET_URLS:
            try:
                log(f"    🔄 Thử faucet: {faucet_url}")

                # Thử các payload khác nhau cho backup faucet
                payloads = [
                    {"address": self.address, "token": "ETH"},
                    {"walletAddress": self.address, "token": "ETH"},
                    {"recipient": self.address, "tokenType": "ETH"},
                    {"wallet": self.address, "selectedToken": "ETH"},
                    {"address": self.address},
                    {"walletAddress": self.address}
                ]

                for payload in payloads:
                    try:
                        async with self.session.post(
                            faucet_url,
                            json=payload,
                            headers={
                                'Content-Type': 'application/json',
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                                'Accept': 'application/json, text/plain, */*',
                                'Accept-Language': 'en-US,en;q=0.9',
                                'Accept-Encoding': 'gzip, deflate, br',
                                'Origin': 'https://faucet.risechain.com',
                                'Referer': 'https://faucet.risechain.com/',
                                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                                'Sec-Ch-Ua-Mobile': '?0',
                                'Sec-Ch-Ua-Platform': '"Windows"',
                                'Sec-Fetch-Dest': 'empty',
                                'Sec-Fetch-Mode': 'cors',
                                'Sec-Fetch-Site': 'same-origin'
                            },
                            timeout=30
                        ) as response:
                            if response.status == 200:
                                result = await response.json()
                                log(f"    ✅ Faucet thành công! Response: {result}")
                                return True
                            elif response.status == 429:
                                log(f"    ⏰ Rate limit - thử lại sau")
                                continue
                            else:
                                text = await response.text()
                                log(f"    ❌ Lỗi {response.status}: {text}")
                    except Exception as e:
                        log(f"    ❌ Lỗi với payload {payload}: {str(e)}")
                        continue

            except Exception as e:
                log(f"    ❌ Lỗi khi gọi {faucet_url}: {str(e)}")
                continue

        # Thử phương pháp GET request
        for faucet_url in ETH_FAUCET_URLS:
            try:
                get_url = f"{faucet_url}?address={self.address}"
                log(f"    🔄 Thử GET request: {get_url}")

                async with self.session.get(
                    get_url,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Referer': 'https://faucet.risechain.com/',
                        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                        'Sec-Ch-Ua-Mobile': '?0',
                        'Sec-Ch-Ua-Platform': '"Windows"',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin'
                    },
                    timeout=30
                ) as response:
                    if response.status == 200:
                        result = await response.text()
                        log(f"    ✅ Faucet thành công! Response: {result}")
                        return True
                    else:
                        text = await response.text()
                        log(f"    ❌ Lỗi {response.status}: {text}")

            except Exception as e:
                log(f"    ❌ Lỗi GET request {faucet_url}: {str(e)}")
                continue

        log(f"    ❌ Không thể lấy ETH từ bất kỳ faucet nào")

        # Thử phương pháp cuối cùng - mở browser để user tự làm
        log(f"    🌐 Mở trang faucet để bạn tự claim ETH...")
        log(f"    📋 Địa chỉ ví của bạn: {self.address}")
        log(f"    🔗 Truy cập: https://faucet.risechain.com/")
        log(f"    📝 Hướng dẫn:")
        log(f"       1. Nhập địa chỉ ví: {self.address}")
        log(f"       2. Click nút 'Drip' hoặc 'Get Tokens'")
        log(f"       3. Chờ giao dịch được xác nhận")

        return False

    async def faucet_with_captcha_bypass(self, yescaptcha_api_key):
        """Thử faucet với YesCaptcha bypass Cloudflare Turnstile."""
        log(f"    🚀 Thử bypass Cloudflare Turnstile với YesCaptcha...")

        try:
            # 1. Lấy trang faucet để tìm site key
            faucet_url = "https://faucet.risechain.com/"
            async with self.session.get(faucet_url) as response:
                if response.status != 200:
                    log(f"    ❌ Không thể truy cập faucet: {response.status}")
                    return False

                html = await response.text()

                # Tìm Turnstile site key
                site_key_match = re.search(r'data-sitekey="([^"]+)"', html)
                if not site_key_match:
                    # Thử pattern khác
                    site_key_match = re.search(r'sitekey["\']?\s*:\s*["\']([^"\']+)["\']', html)

                if not site_key_match:
                    log(f"    ❌ Không tìm thấy Turnstile site key")
                    return False

                site_key = site_key_match.group(1)
                log(f"    🔑 Tìm thấy site key: {site_key[:20]}...")

            # 2. Giải captcha với YesCaptcha
            captcha_solver = YesCaptchaSolver(yescaptcha_api_key)
            captcha_token = await captcha_solver.solve_turnstile(self.session, site_key, faucet_url)

            if not captcha_token:
                log(f"    ❌ Không thể giải captcha")
                return False

            log(f"    ✅ Đã có captcha token: {captcha_token[:20]}...")

            # 3. Thử gửi request với captcha token
            success = await self.submit_faucet_with_token(captcha_token)
            return success

        except Exception as e:
            log(f"    ❌ Lỗi bypass captcha: {str(e)}")
            return False

    async def submit_faucet_with_token(self, captcha_token):
        """Gửi request faucet với captcha token."""
        endpoints_to_try = [
            "/api/faucet",
            "/api/drip",
            "/api/claim",
            "/api/request",
            "/faucet",
            "/drip"
        ]

        for endpoint in endpoints_to_try:
            url = f"https://faucet.risechain.com{endpoint}"

            # Thử các payload khác nhau
            payloads = [
                {
                    "address": self.address,
                    "cf-turnstile-response": captcha_token
                },
                {
                    "walletAddress": self.address,
                    "turnstile": captcha_token
                },
                {
                    "recipient": self.address,
                    "captcha": captcha_token
                },
                {
                    "address": self.address,
                    "token": captcha_token
                }
            ]

            for payload in payloads:
                try:
                    headers = {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/plain, */*',
                        'Origin': 'https://faucet.risechain.com',
                        'Referer': 'https://faucet.risechain.com/',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin'
                    }

                    async with self.session.post(url, json=payload, headers=headers) as response:
                        text = await response.text()

                        if response.status == 200:
                            log(f"    🎉 Faucet thành công! Response: {text}")
                            return True
                        elif response.status == 429:
                            log(f"    ⏰ Rate limited")
                            continue
                        elif response.status not in [404, 405]:
                            log(f"    🔍 {endpoint} - {response.status}: {text[:100]}")

                except Exception as e:
                    continue

        return False

    async def close_session(self):
        """Đóng session HTTP."""
        if self.session:
            await self.session.close()

    async def approve_token(self, token_symbol: str, amount: float):
        """Phê duyệt (approve) cho Swap Router được sử dụng token."""
        log(f"  🔑 Đang phê duyệt cho Router sử dụng {token_symbol}...")
        try:
            token_address = self.w3.to_checksum_address(TOKEN_ADDRESSES[token_symbol.upper()])
            token_contract = self.w3.eth.contract(address=token_address, abi=ERC20_ABI)
            decimals = await token_contract.functions.decimals().call()
            amount_wei = int(amount * (10**decimals))

            tx_data = await token_contract.functions.approve(
                self.w3.to_checksum_address(SWAP_ROUTER_ADDRESS),
                amount_wei
            ).build_transaction({
                'from': self.address,
                'nonce': await self.w3.eth.get_transaction_count(self.address),
                'gas': 100000,
                'gasPrice': await self.w3.eth.gas_price,
                'chainId': await self.w3.eth.chain_id
            })
            return await self._send_transaction(tx_data)
        except KeyError:
            log(f"    ❌ Lỗi: Token '{token_symbol}' không được định nghĩa trong TOKEN_ADDRESSES.")
            return None
        except Exception as e:
            log(f"    ❌ Lỗi khi approve token: {str(e)}")
            return None

    async def swap_tokens(self, from_symbol: str, to_symbol: str, amount: float):
        """Thực hiện swap token."""
        log(f"  🔄 Đang swap {amount} {from_symbol} -> {to_symbol}...")
        try:
            from_address = self.w3.to_checksum_address(TOKEN_ADDRESSES[from_symbol.upper()])
            to_address = self.w3.to_checksum_address(TOKEN_ADDRESSES[to_symbol.upper()])
            
            token_contract = self.w3.eth.contract(address=from_address, abi=ERC20_ABI)
            decimals = await token_contract.functions.decimals().call()
            amount_in_wei = int(amount * (10**decimals))

            router_contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(SWAP_ROUTER_ADDRESS),
                abi=SWAP_ROUTER_ABI
            )
            
            tx_data = await router_contract.functions.swapExactTokensForTokens(
                amount_in_wei,
                0,  # amountOutMin: chấp nhận mọi kết quả
                [from_address, to_address],
                self.address,
                int(time.time()) + 600  # Deadline: 10 phút
            ).build_transaction({
                'from': self.address,
                'nonce': await self.w3.eth.get_transaction_count(self.address),
                'gas': 300000,
                'gasPrice': await self.w3.eth.gas_price,
                'chainId': await self.w3.eth.chain_id
            })
            return await self._send_transaction(tx_data)
        except KeyError as e:
            log(f"    ❌ Lỗi: Một trong các token '{from_symbol}', '{to_symbol}' không được định nghĩa.")
            return None
        except Exception as e:
            log(f"    ❌ Lỗi khi swap: {str(e)}")
            return None

def log(message):
    """Hàm ghi log có định dạng thời gian và màu sắc."""
    timestamp = datetime.now(VN_TZ).strftime('%Y-%m-%d %H:%M:%S')
    print(f"{Fore.CYAN}[{timestamp}]{Style.RESET_ALL} | {message}")

def get_user_config():
    """Lấy cấu hình từ người dùng qua giao diện dòng lệnh."""
    os.system('cls' if os.name == 'nt' else 'clear')
    print(Fore.BLUE + Style.BRIGHT + "="*60)
    print(Fore.CYAN + Style.BRIGHT + "Risechain Bot (Faucet & Swap)".center(60))
    print(Fore.BLUE + Style.BRIGHT + "="*60 + "\n")
    print(f"{Fore.YELLOW}Chọn chức năng bạn muốn thực hiện:{Style.RESET_ALL}")
    print("  1. Drip Faucet (Nhận token miễn phí)")
    print("  2. Swap Tokens (Hoán đổi token)")
    print("  3. Faucet ETH (Nhận ETH testnet)")

    while True:
        choice = input("  Lựa chọn của bạn [1-3]: ").strip()
        if choice in ['1', '2', '3']:
            break
        print(f"{Fore.RED}  Vui lòng chọn 1, 2 hoặc 3!{Style.RESET_ALL}")
    
    while True:
        try:
            min_delay = int(input("  Nhập thời gian chờ TỐI THIỂU giữa các ví (giây): "))
            max_delay = int(input("  Nhập thời gian chờ TỐI ĐA giữa các ví (giây): "))
            if min_delay >= 0 and max_delay >= min_delay:
                break
            print(f"{Fore.RED}  Thời gian chờ không hợp lệ!{Style.RESET_ALL}")
        except ValueError:
            print(f"{Fore.RED}  Vui lòng nhập số!{Style.RESET_ALL}")
    
    config = {'choice': choice, 'min_delay': min_delay, 'max_delay': max_delay}

    if choice == '1':
        print(f"\n{Fore.YELLOW}Các token có sẵn: {', '.join(TOKEN_ADDRESSES.keys())}{Style.RESET_ALL}")
        while True:
            token = input("  Nhập ký hiệu token muốn drip: ").upper().strip()
            if token in TOKEN_ADDRESSES:
                config['token_symbol'] = token
                break
            print(f"{Fore.RED}  Token không hợp lệ!{Style.RESET_ALL}")
    
    elif choice == '2':
        print(f"\n{Fore.YELLOW}Các token có sẵn: {', '.join(TOKEN_ADDRESSES.keys())}{Style.RESET_ALL}")
        
        while True:
            from_token = input("  Nhập ký hiệu token muốn BÁN: ").upper().strip()
            if from_token in TOKEN_ADDRESSES:
                config['from_symbol'] = from_token
                break
            print(f"{Fore.RED}  Token không hợp lệ!{Style.RESET_ALL}")
        
        while True:
            to_token = input("  Nhập ký hiệu token muốn MUA: ").upper().strip()
            if to_token in TOKEN_ADDRESSES and to_token != from_token:
                config['to_symbol'] = to_token
                break
            print(f"{Fore.RED}  Token không hợp lệ hoặc trùng với token nguồn!{Style.RESET_ALL}")
        
        while True:
            try:
                amount = float(input(f"  Nhập số lượng {from_token} muốn swap: "))
                if amount > 0:
                    config['amount'] = amount
                    break
                print(f"{Fore.RED}  Số lượng phải lớn hơn 0!{Style.RESET_ALL}")
            except ValueError:
                print(f"{Fore.RED}  Vui lòng nhập số!{Style.RESET_ALL}")
        
        approve = input("  Bạn có muốn thực hiện Approve trước không? (y/n): ").lower().strip()
        config['approve_first'] = approve == 'y'

    elif choice == '3':  # Faucet ETH
        print(f"\n{Fore.YELLOW}Cấu hình YesCaptcha để bypass Cloudflare Turnstile:{Style.RESET_ALL}")
        yescaptcha_key = input("  Nhập YesCaptcha API key (để trống nếu không có): ").strip()
        config['yescaptcha_api_key'] = yescaptcha_key if yescaptcha_key else None

        if not yescaptcha_key:
            print(f"{Fore.YELLOW}  ⚠️  Không có API key - sẽ hướng dẫn claim manual{Style.RESET_ALL}")

    return config

async def main():
    """Hàm chính điều khiển luồng chạy của bot."""
    # Kiểm tra cấu hình
    if "0x..." in [FAUCET_CONTRACT_ADDRESS, SWAP_ROUTER_ADDRESS] + list(TOKEN_ADDRESSES.values()):
        log(f"{Fore.RED}Lỗi: Vui lòng cập nhật đầy đủ các địa chỉ contract và token trong file code trước khi chạy.{Style.RESET_ALL}")
        return

    # Đọc danh sách private keys
    try:
        with open('accounts.txt', 'r', encoding='utf-8') as f:
            private_keys = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]
        if not private_keys:
            log(f"{Fore.RED}Lỗi: File 'accounts.txt' trống!{Style.RESET_ALL}")
            return
    except FileNotFoundError:
        log(f"{Fore.RED}Lỗi: Không tìm thấy file 'accounts.txt'. Vui lòng tạo file và thêm private key vào.{Style.RESET_ALL}")
        return

    # Đọc danh sách proxy (nếu có)
    proxies = []
    try:
        with open('proxies.txt', 'r', encoding='utf-8') as f:
            proxies = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]
    except FileNotFoundError:
        log(f"{Fore.YELLOW}Cảnh báo: Không tìm thấy file 'proxies.txt'. Tiếp tục mà không sử dụng proxy.{Style.RESET_ALL}")

    # Lấy cấu hình từ người dùng
    config = get_user_config()
    
    log(f"{Fore.MAGENTA}Bắt đầu xử lý {len(private_keys)} tài khoản...{Style.RESET_ALL}")
    print(Fore.BLUE + Style.BRIGHT + "="*60 + Style.RESET_ALL)

    # Xử lý từng tài khoản
    for i, pk in enumerate(private_keys, 1):
        try:
            # Bỏ qua dòng trống hoặc comment
            if not pk or pk.startswith('#'):
                continue
                
            proxy = proxies[(i-1) % len(proxies)] if proxies else None
            
            log(f"{Fore.CYAN}--- Đang xử lý tài khoản {i}/{len(private_keys)} ---{Style.RESET_ALL}")
            bot = RiseChainBot(private_key=pk, proxy=proxy)
            log(f"  Địa chỉ ví: {bot.address}")

            if config['choice'] == '1':  # Drip Faucet
                await bot.drip_faucet(config['token_symbol'])

            elif config['choice'] == '2':  # Swap Tokens
                if config['approve_first']:
                    await bot.approve_token(config['from_symbol'], config['amount'])
                    log(f"    Đang chờ 15 giây để giao dịch approve được xác nhận...")
                    await asyncio.sleep(15)

                await bot.swap_tokens(config['from_symbol'], config['to_symbol'], config['amount'])

            elif config['choice'] == '3':  # Faucet ETH
                await bot.faucet_eth(config.get('yescaptcha_api_key'))
                await bot.close_session()

            # Tạm dừng giữa các tài khoản
            if i < len(private_keys):
                delay = random.randint(config['min_delay'], config['max_delay'])
                log(f"Đang chờ {delay} giây trước khi xử lý tài khoản tiếp theo...")
                await asyncio.sleep(delay)
            
        except Exception as e:
            log(f"{Fore.RED}  Đã xảy ra lỗi với tài khoản {i}: {str(e)}{Style.RESET_ALL}")
            if i < len(private_keys):
                log(f"{Fore.YELLOW}  Đang chờ 10 giây trước khi tiếp tục...{Style.RESET_ALL}")
                await asyncio.sleep(10)
        
        print(Fore.BLUE + Style.BRIGHT + "-"*60 + Style.RESET_ALL)

    log(f"{Fore.GREEN+Style.BRIGHT}Đã hoàn thành tất cả các tác vụ!{Style.RESET_ALL}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nChương trình đã bị dừng bởi người dùng.")
    except Exception as e:
        print(f"\n{Fore.RED}Đã xảy ra lỗi không mong muốn: {str(e)}{Style.RESET_ALL}")
    finally:
        input("\nNhấn Enter để thoát...")
