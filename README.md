# Rise<PERSON><PERSON><PERSON>t tự động tương tác với mạng lưới <PERSON>, hỗ trợ các chức năng:
- Nhận token miễn phí từ Faucet
- <PERSON><PERSON> đổi token (Swap) trên DEX

## Cài đặt

1. <PERSON><PERSON>i đặt Python 3.8 hoặc cao hơn
2. <PERSON><PERSON><PERSON> đặt các thư viện cần thiết:
   ```
   pip install -r requirements.txt
   ```

## Cấu hình

1. Thêm private key vào file `accounts.txt` (mỗi dòng một key)
2. (<PERSON><PERSON><PERSON> chọn) Thêm proxy vào file `proxies.txt` nếu cần

## Cách sử dụng

1. <PERSON><PERSON><PERSON> chương trình:
   ```
   python rise_bot.py
   ```

2. <PERSON>à<PERSON> theo hướng dẫn trên màn hình để chọn chức năng:
   - Dr<PERSON>t: Nhận token miễn phí
   - Swap Tokens: <PERSON><PERSON> đổi giữa các token

## Lư<PERSON> ý bảo mật

- <PERSON><PERSON><PERSON><PERSON> chia sẻ file `accounts.txt` chứa private key của bạn
- <PERSON><PERSON> dụng proxy nếu cần thiết để bảo vệ địa chỉ IP
- Kiểm tra kỹ các giao dịch trước khi xác nhận

## Hỗ trợ

Nếu gặp lỗi hoặc cần hỗ trợ, vui lòng tạo issue trên repository hoặc liên hệ với đội ngũ phát triển.
